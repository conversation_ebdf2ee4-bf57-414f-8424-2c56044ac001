/**
 * نظام الطباعة الحرارية - تكنوفلاش POS
 * يدعم طباعة الفواتير وملصقات الباركود على الطابعات الحرارية
 */

/**
 * أوامر ESC/POS المحسنة للطابعات الحرارية
 * متوافقة مع معظم طابعات الكاشير الحرارية
 */
const ESC_POS_COMMANDS = {
    // أوامر التهيئة المحسنة
    INIT: '\x1B\x40',                    // تهيئة الطابعة
    RESET: '\x1B\x40',                   // إعادة تعيين
    WAKE_UP: '\x1B\x3D\x01',            // تنشيط الطابعة

    // أوامر النص المحسنة
    BOLD_ON: '\x1B\x45\x01',            // تشغيل الخط العريض
    BOLD_OFF: '\x1B\x45\x00',           // إيقاف الخط العريض
    UNDERLINE_ON: '\x1B\x2D\x01',       // تشغيل التسطير
    UNDERLINE_OFF: '\x1B\x2D\x00',      // إيقاف التسطير
    DOUBLE_HEIGHT: '\x1B\x21\x10',      // ضعف الارتفاع
    DOUBLE_WIDTH: '\x1B\x21\x20',       // ضعف العرض
    DOUBLE_SIZE: '\x1B\x21\x30',        // ضعف الحجم (عرض + ارتفاع)
    NORMAL_SIZE: '\x1B\x21\x00',        // الحجم العادي
    SMALL_FONT: '\x1B\x4D\x01',         // خط صغير
    NORMAL_FONT: '\x1B\x4D\x00',        // خط عادي

    // أوامر المحاذاة
    ALIGN_LEFT: '\x1B\x61\x00',         // محاذاة يسار
    ALIGN_CENTER: '\x1B\x61\x01',       // محاذاة وسط
    ALIGN_RIGHT: '\x1B\x61\x02',        // محاذاة يمين

    // أوامر القطع المحسنة
    CUT_FULL: '\x1D\x56\x00',           // قطع كامل
    CUT_PARTIAL: '\x1D\x56\x01',        // قطع جزئي
    CUT_FEED: '\x1D\x56\x41',           // قطع مع تغذية الورق
    CUT_FEED_ALT: '\x1D\x56\x42',       // قطع بديل مع تغذية

    // أوامر التغذية المحسنة
    LINE_FEED: '\x0A',                  // سطر جديد
    FORM_FEED: '\x0C',                  // صفحة جديدة
    FEED_LINES: (n) => '\x1B\x64' + String.fromCharCode(Math.min(n, 255)), // تغذية n أسطر (محدود بـ 255)
    FEED_DOTS: (n) => '\x1B\x4A' + String.fromCharCode(Math.min(n, 255)),  // تغذية n نقطة

    // أوامر الباركود المحسنة
    BARCODE_HEIGHT: (h) => '\x1D\x68' + String.fromCharCode(Math.min(Math.max(h, 1), 255)), // ارتفاع الباركود (1-255)
    BARCODE_WIDTH: (w) => '\x1D\x77' + String.fromCharCode(Math.min(Math.max(w, 1), 6)),    // عرض الباركود (1-6)
    BARCODE_POSITION: '\x1D\x48\x02',   // موضع النص تحت الباركود
    BARCODE_POSITION_NONE: '\x1D\x48\x00', // بدون نص
    BARCODE_POSITION_ABOVE: '\x1D\x48\x01', // نص فوق الباركود
    BARCODE_POSITION_BELOW: '\x1D\x48\x02', // نص تحت الباركود
    BARCODE_POSITION_BOTH: '\x1D\x48\x03',  // نص فوق وتحت
    BARCODE_CODE128: '\x1D\x6B\x49',    // باركود Code128
    BARCODE_EAN13: '\x1D\x6B\x43',      // باركود EAN13
    BARCODE_EAN8: '\x1D\x6B\x44',       // باركود EAN8
    BARCODE_UPC_A: '\x1D\x6B\x41',      // باركود UPC-A

    // أوامر إضافية للتوافق
    DRAWER_OPEN: '\x1B\x70\x00\x19\xFA', // فتح درج النقود
    BEEP: '\x1B\x42\x05\x05',           // صوت تنبيه
};

/**
 * إعدادات الطباعة الحرارية
 */
const THERMAL_SETTINGS = {
    // عرض الورق بالأحرف (58mm = 32 حرف تقريباً)
    PAPER_WIDTH_58MM: 32,
    PAPER_WIDTH_80MM: 48,
    
    // إعدادات الباركود
    BARCODE_HEIGHT: 50,
    BARCODE_WIDTH: 2,
    
    // إعدادات الملصق
    LABEL_WIDTH: 32,
    LABEL_HEIGHT: 8,
};

/**
 * طباعة فاتورة حرارية محسنة بحجم 58mm
 * متوافقة مع جميع طابعات الكاشير الحرارية
 */
function printThermalInvoice(saleData) {
    try {
        console.log('🖨️ بدء طباعة الفاتورة الحرارية المحسنة...');

        const sale = typeof saleData === 'string' ? db.getSale(saleData) : saleData;
        if (!sale) {
            throw new Error('لم يتم العثور على بيانات البيع');
        }

        const customer = sale.customerId !== 'guest' ? db.getCustomer(sale.customerId) : null;
        const settings = db.getSettings();
        const companyInfo = settings.company || {};

        // إنشاء رقم فاتورة
        const invoiceNumber = sale.invoiceNumber || `INV-${new Date().getFullYear()}-${sale.id.substring(0, 8)}`;
        const currentDate = new Date(sale.date);

        // بناء محتوى الفاتورة الحرارية المحسن
        let thermalContent = '';

        // تهيئة الطابعة وإعادة تعيين
        thermalContent += ESC_POS_COMMANDS.INIT;
        thermalContent += ESC_POS_COMMANDS.RESET;

        // رأسية الفاتورة - اسم المتجر (محسن للعرض 58mm)
        thermalContent += ESC_POS_COMMANDS.ALIGN_CENTER;
        thermalContent += ESC_POS_COMMANDS.DOUBLE_HEIGHT;
        thermalContent += ESC_POS_COMMANDS.BOLD_ON;
        const companyName = truncateText(companyInfo.companyName || 'متجر تكنوفلاش', 16);
        thermalContent += companyName + '\n';
        thermalContent += ESC_POS_COMMANDS.NORMAL_SIZE;
        thermalContent += ESC_POS_COMMANDS.BOLD_OFF;

        // معلومات الشركة (محسنة للعرض الضيق)
        if (companyInfo.phone) {
            const phoneText = truncateText('هاتف: ' + companyInfo.phone, 32);
            thermalContent += phoneText + '\n';
        }
        if (companyInfo.address) {
            const addressText = truncateText(companyInfo.address, 32);
            thermalContent += addressText + '\n';
        }
        if (companyInfo.taxNumber) {
            const taxText = truncateText('ض.ب: ' + companyInfo.taxNumber, 32);
            thermalContent += taxText + '\n';
        }

        // خط فاصل محسن
        thermalContent += ESC_POS_COMMANDS.ALIGN_LEFT;
        thermalContent += repeatChar('=', 32) + '\n';

        // معلومات الفاتورة (محسنة)
        thermalContent += ESC_POS_COMMANDS.ALIGN_CENTER;
        thermalContent += ESC_POS_COMMANDS.BOLD_ON;
        thermalContent += 'فاتورة مبيعات\n';
        thermalContent += ESC_POS_COMMANDS.BOLD_OFF;
        thermalContent += ESC_POS_COMMANDS.ALIGN_LEFT;

        // رقم الفاتورة والتاريخ (في سطرين منفصلين للوضوح)
        thermalContent += `رقم: ${truncateText(invoiceNumber, 24)}\n`;
        thermalContent += `تاريخ: ${formatDateForThermal(currentDate)}\n`;
        thermalContent += `وقت: ${currentDate.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'})}\n`;

        // معلومات العميل (محسنة)
        if (customer) {
            const customerName = truncateText('عميل: ' + customer.name, 32);
            thermalContent += customerName + '\n';
            if (customer.phone) {
                const customerPhone = truncateText('ت: ' + customer.phone, 32);
                thermalContent += customerPhone + '\n';
            }
        }

        thermalContent += repeatChar('=', 32) + '\n';

        // رأسية الجدول (محسنة للعرض 58mm)
        thermalContent += ESC_POS_COMMANDS.BOLD_ON;
        thermalContent += padText('المنتج', 14) + padText('ك', 3) + padText('سعر', 7) + padText('مجموع', 8) + '\n';
        thermalContent += ESC_POS_COMMANDS.BOLD_OFF;
        thermalContent += repeatChar('-', 32) + '\n';

        // عناصر الفاتورة (محسنة)
        let subtotal = 0;
        sale.items.forEach(item => {
            const product = db.getProduct(item.productId);
            const productName = product ? product.name : 'منتج غير معروف';
            const itemTotal = item.quantity * item.price;
            subtotal += itemTotal;

            // اسم المنتج (مقطوع للعرض المناسب)
            const truncatedName = truncateText(productName, 14);
            thermalContent += padText(truncatedName, 14);
            thermalContent += padText(item.quantity.toString(), 3);
            thermalContent += padText(formatCurrencyForThermal(item.price), 7);
            thermalContent += padText(formatCurrencyForThermal(itemTotal), 8) + '\n';
        });

        thermalContent += repeatChar('-', 32) + '\n';

        // المجاميع (محسنة للعرض 58mm)
        thermalContent += ESC_POS_COMMANDS.BOLD_ON;
        thermalContent += padText('المجموع الفرعي:', 18) + padText(formatCurrencyForThermal(subtotal), 14) + '\n';

        // الضرائب
        if (sale.tax && sale.tax > 0) {
            thermalContent += padText('الضريبة:', 18) + padText(formatCurrencyForThermal(sale.tax), 14) + '\n';
        }

        // الخصم
        if (sale.discount && sale.discount > 0) {
            thermalContent += padText('الخصم:', 18) + padText('-' + formatCurrencyForThermal(sale.discount), 14) + '\n';
        }

        // المجموع النهائي (محسن)
        thermalContent += repeatChar('=', 32) + '\n';
        thermalContent += ESC_POS_COMMANDS.DOUBLE_HEIGHT;
        thermalContent += ESC_POS_COMMANDS.ALIGN_CENTER;
        thermalContent += 'الإجمالي: ' + formatCurrencyForThermal(sale.total) + '\n';
        thermalContent += ESC_POS_COMMANDS.NORMAL_SIZE;
        thermalContent += ESC_POS_COMMANDS.ALIGN_LEFT;
        thermalContent += ESC_POS_COMMANDS.BOLD_OFF;

        // طريقة الدفع (محسنة)
        thermalContent += repeatChar('=', 32) + '\n';
        const paymentMethod = getPaymentMethodText(sale.paymentMethod);
        thermalContent += `طريقة الدفع: ${truncateText(paymentMethod, 20)}\n`;

        if (sale.amountPaid && sale.amountPaid !== sale.total) {
            thermalContent += `المدفوع: ${formatCurrencyForThermal(sale.amountPaid)}\n`;
            const change = sale.amountPaid - sale.total;
            if (change > 0) {
                thermalContent += `الباقي: ${formatCurrencyForThermal(change)}\n`;
            }
        }

        // تذييل الفاتورة (محسن)
        thermalContent += '\n';
        thermalContent += ESC_POS_COMMANDS.ALIGN_CENTER;
        thermalContent += 'شكراً لتسوقكم معنا\n';
        thermalContent += 'إرجاع خلال 7 أيام\n';
        thermalContent += '\n';
        thermalContent += `${new Date().toLocaleDateString('ar-SA')} ${new Date().toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'})}\n`;
        thermalContent += 'تكنوفلاش POS\n';

        // تغذية الورق وقطع تلقائي محسن
        thermalContent += '\n\n';
        thermalContent += ESC_POS_COMMANDS.FEED_LINES(3);
        thermalContent += ESC_POS_COMMANDS.CUT_FEED;
        
        // إرسال للطباعة
        sendToThermalPrinter(thermalContent, 'فاتورة مبيعات');
        
        console.log('✅ تم إنشاء الفاتورة الحرارية بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في طباعة الفاتورة الحرارية:', error);
        app.showAlert('خطأ', 'فشل في طباعة الفاتورة الحرارية: ' + error.message);
    }
}

/**
 * طباعة ملصق باركود محسن للمنتج - حجم صغير (40mm x 30mm)
 */
function printProductBarcodeLabel(productId) {
    try {
        console.log('🏷️ بدء طباعة ملصق الباركود المحسن...');

        const product = db.getProduct(productId);
        if (!product) {
            throw new Error('لم يتم العثور على المنتج');
        }

        if (!product.barcode) {
            throw new Error('المنتج لا يحتوي على باركود');
        }

        // استخدام الوظيفة المحسنة للملصقات الصغيرة
        printSmallBarcodeLabel(product);

        console.log('✅ تم إنشاء ملصق الباركود المحسن بنجاح');

    } catch (error) {
        console.error('❌ خطأ في طباعة ملصق الباركود:', error);
        app.showAlert('خطأ', 'فشل في طباعة ملصق الباركود: ' + error.message);
    }
}

/**
 * طباعة ملصق باركود صغير محسن (40mm x 30mm)
 */
function printSmallBarcodeLabel(product) {
    try {
        // بناء محتوى الملصق المحسن
        let labelContent = '';

        // تهيئة الطابعة
        labelContent += ESC_POS_COMMANDS.INIT;
        labelContent += ESC_POS_COMMANDS.RESET;

        // اسم المنتج (مختصر ومحسن للحجم الصغير)
        labelContent += ESC_POS_COMMANDS.ALIGN_CENTER;
        labelContent += ESC_POS_COMMANDS.BOLD_ON;
        const truncatedName = truncateText(product.name, 20); // أقصر للملصق الصغير
        labelContent += truncatedName + '\n';
        labelContent += ESC_POS_COMMANDS.BOLD_OFF;

        // إعدادات الباركود المحسنة للحجم الصغير
        const smallBarcodeHeight = 30; // أقل من الافتراضي
        const smallBarcodeWidth = 1;   // أرفع للوضوح

        labelContent += ESC_POS_COMMANDS.BARCODE_HEIGHT(smallBarcodeHeight);
        labelContent += ESC_POS_COMMANDS.BARCODE_WIDTH(smallBarcodeWidth);
        labelContent += ESC_POS_COMMANDS.BARCODE_POSITION;

        // طباعة الباركود مع تحسين للحجم الصغير
        if (product.barcode.length === 13 && /^\d+$/.test(product.barcode)) {
            labelContent += ESC_POS_COMMANDS.BARCODE_EAN13;
        } else {
            labelContent += ESC_POS_COMMANDS.BARCODE_CODE128;
        }

        labelContent += String.fromCharCode(product.barcode.length) + product.barcode;
        labelContent += '\n';

        // رقم الباركود (بخط أصغر)
        labelContent += ESC_POS_COMMANDS.ALIGN_CENTER;
        labelContent += product.barcode + '\n';

        // السعر (اختياري للملصق الصغير)
        if (product.price) {
            labelContent += formatCurrencyForThermal(product.price) + '\n';
        }

        // تغذية وقطع محسن للملصق الصغير
        labelContent += ESC_POS_COMMANDS.FEED_LINES(1);
        labelContent += ESC_POS_COMMANDS.CUT_FEED;

        // إرسال للطباعة
        sendToThermalPrinter(labelContent, 'ملصق باركود صغير');

    } catch (error) {
        throw new Error('فشل في إنشاء ملصق الباركود الصغير: ' + error.message);
    }
}

/**
 * إرسال البيانات للطابعة الحرارية مع معالجة محسنة للأخطاء
 */
function sendToThermalPrinter(content, title, options = {}) {
    const {
        paperSize = '58mm',
        fontSize = '12px',
        timeout = 1000,
        retryCount = 2
    } = options;

    return new Promise((resolve, reject) => {
        let attempts = 0;

        function attemptPrint() {
            attempts++;

            try {
                console.log(`🖨️ محاولة طباعة ${attempts}/${retryCount + 1}: ${title}`);

                // التحقق من دعم المتصفح للطباعة
                if (!window.print) {
                    throw new Error('المتصفح لا يدعم الطباعة');
                }

                // إنشاء نافذة طباعة محسنة
                const printWindow = window.open('', '_blank', 'width=300,height=400,scrollbars=yes');

                if (!printWindow) {
                    throw new Error('فشل في فتح نافذة الطباعة - قد يكون محجوبة بواسطة مانع النوافذ المنبثقة');
                }

                const htmlContent = createThermalPrintHTML(content, title, paperSize, fontSize);
                printWindow.document.write(htmlContent);
                printWindow.document.close();

                // معالج الأحداث للطباعة
                printWindow.onload = function() {
                    setTimeout(() => {
                        try {
                            printWindow.print();

                            // إغلاق النافذة بعد الطباعة
                            setTimeout(() => {
                                if (!printWindow.closed) {
                                    printWindow.close();
                                }
                                resolve(true);
                            }, timeout);

                        } catch (printError) {
                            console.error('خطأ في الطباعة:', printError);
                            if (attempts <= retryCount) {
                                setTimeout(attemptPrint, 500);
                            } else {
                                showFallbackPrintDialog(content, title);
                                reject(printError);
                            }
                        }
                    }, 500);
                };

                // معالج خطأ النافذة
                printWindow.onerror = function(error) {
                    console.error('خطأ في نافذة الطباعة:', error);
                    if (attempts <= retryCount) {
                        setTimeout(attemptPrint, 500);
                    } else {
                        showFallbackPrintDialog(content, title);
                        reject(error);
                    }
                };

            } catch (error) {
                console.error(`خطأ في المحاولة ${attempts}:`, error);

                if (attempts <= retryCount) {
                    setTimeout(attemptPrint, 500);
                } else {
                    showFallbackPrintDialog(content, title);
                    reject(error);
                }
            }
        }

        attemptPrint();
    });
}

/**
 * إنشاء HTML محسن للطباعة الحرارية
 */
function createThermalPrintHTML(content, title, paperSize, fontSize) {
    return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${title}</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                body {
                    font-family: 'Courier New', 'Consolas', monospace;
                    font-size: ${fontSize};
                    line-height: 1.2;
                    white-space: pre-wrap;
                    direction: ltr;
                    background: white;
                    color: black;
                }
                @media print {
                    body {
                        margin: 0;
                        padding: 0;
                    }
                    @page {
                        margin: 0;
                        size: ${paperSize} auto;
                    }
                }
                @media screen {
                    body {
                        padding: 10px;
                        max-width: 300px;
                        margin: 0 auto;
                        border: 1px solid #ccc;
                        background: #f9f9f9;
                    }
                }
            </style>
        </head>
        <body>${content}</body>
        </html>
    `;
}

/**
 * عرض نافذة طباعة بديلة في حالة الفشل
 */
function showFallbackPrintDialog(content, title) {
    try {
        const fallbackWindow = window.open('', '_blank', 'width=400,height=600,scrollbars=yes');

        if (fallbackWindow) {
            const fallbackHTML = `
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>${title} - طباعة بديلة</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            padding: 20px;
                            direction: rtl;
                        }
                        .content {
                            font-family: 'Courier New', monospace;
                            white-space: pre-wrap;
                            border: 1px solid #ccc;
                            padding: 15px;
                            background: #f9f9f9;
                            margin: 20px 0;
                            direction: ltr;
                        }
                        .buttons {
                            text-align: center;
                            margin: 20px 0;
                        }
                        button {
                            padding: 10px 20px;
                            margin: 0 10px;
                            font-size: 14px;
                            cursor: pointer;
                        }
                        .print-btn {
                            background: #007bff;
                            color: white;
                            border: none;
                            border-radius: 4px;
                        }
                        .close-btn {
                            background: #6c757d;
                            color: white;
                            border: none;
                            border-radius: 4px;
                        }
                        .error-msg {
                            color: #dc3545;
                            background: #f8d7da;
                            border: 1px solid #f5c6cb;
                            padding: 10px;
                            border-radius: 4px;
                            margin-bottom: 20px;
                        }
                    </style>
                </head>
                <body>
                    <h2>${title}</h2>
                    <div class="error-msg">
                        <strong>تنبيه:</strong> فشلت الطباعة التلقائية. يمكنك طباعة المحتوى يدوياً باستخدام الزر أدناه.
                    </div>
                    <div class="content">${content.replace(/\n/g, '<br>')}</div>
                    <div class="buttons">
                        <button class="print-btn" onclick="window.print()">
                            🖨️ طباعة
                        </button>
                        <button class="close-btn" onclick="window.close()">
                            ❌ إغلاق
                        </button>
                    </div>
                </body>
                </html>
            `;

            fallbackWindow.document.write(fallbackHTML);
            fallbackWindow.document.close();
        } else {
            // إذا فشل فتح النافذة البديلة، عرض تنبيه
            alert(`فشل في الطباعة: ${title}\n\nالمحتوى:\n${content}`);
        }

    } catch (error) {
        console.error('فشل في عرض النافذة البديلة:', error);
        alert(`فشل في الطباعة: ${title}\n\nيرجى المحاولة مرة أخرى أو التحقق من إعدادات الطابعة.`);
    }
}

/**
 * دوال مساعدة للتنسيق
 */

/**
 * تنسيق التاريخ للطباعة الحرارية
 */
function formatDateForThermal(date) {
    const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    };
    return date.toLocaleDateString('ar-SA', options);
}

/**
 * تنسيق العملة للطباعة الحرارية
 */
function formatCurrencyForThermal(amount) {
    return parseFloat(amount).toFixed(2) + ' ج.م';
}

/**
 * قطع النص ليناسب العرض المحدد (محسن للنصوص العربية)
 */
function truncateText(text, maxLength) {
    if (!text) return '';
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength - 2) + '..';
}

/**
 * تكرار حرف معين عدد مرات محدد
 */
function repeatChar(char, count) {
    return char.repeat(count);
}

/**
 * تكرار حرف معين عدد مرات محدد
 */
function repeatChar(char, count) {
    return char.repeat(count);
}

/**
 * إضافة مسافات للنص ليصل للطول المطلوب
 */
function padText(text, length, align = 'left') {
    text = text.toString();
    if (text.length >= length) {
        return text.substring(0, length);
    }

    const padding = ' '.repeat(length - text.length);

    switch (align) {
        case 'right':
            return padding + text;
        case 'center':
            const leftPad = Math.floor(padding.length / 2);
            const rightPad = padding.length - leftPad;
            return ' '.repeat(leftPad) + text + ' '.repeat(rightPad);
        default: // left
            return text + padding;
    }
}

/**
 * الحصول على نص طريقة الدفع
 */
function getPaymentMethodText(method) {
    const methods = {
        'cash': 'نقدي',
        'card': 'بطاقة ائتمان',
        'bank': 'تحويل بنكي',
        'installment': 'تقسيط'
    };
    return methods[method] || 'غير محدد';
}

/**
 * طباعة فاتورة عادية (A4) - محسنة
 */
function printStandardInvoice(saleData) {
    try {
        console.log('🖨️ بدء طباعة الفاتورة العادية...');

        const sale = typeof saleData === 'string' ? db.getSale(saleData) : saleData;
        if (!sale) {
            throw new Error('لم يتم العثور على بيانات البيع');
        }

        const customer = sale.customerId !== 'guest' ? db.getCustomer(sale.customerId) : null;
        const settings = db.getSettings();
        const companyInfo = settings.company || {};

        // إنشاء رقم فاتورة
        const invoiceNumber = sale.invoiceNumber || `INV-${new Date().getFullYear()}-${sale.id.substring(0, 8)}`;
        const currentDate = new Date(sale.date);
        const hijriDate = db.toHijriDate ? db.toHijriDate(currentDate) : '';

        const invoiceContent = `
            <div class="invoice">
                <div class="invoice-header">
                    <div class="company-info">
                        <h1>${companyInfo.companyName || 'متجر تكنوفلاش'}</h1>
                        ${companyInfo.phone ? `<p class="phone">هاتف: ${companyInfo.phone}</p>` : ''}
                        ${companyInfo.address ? `<p class="address">${companyInfo.address}</p>` : ''}
                        ${companyInfo.email ? `<p class="email">البريد الإلكتروني: ${companyInfo.email}</p>` : ''}
                        ${companyInfo.taxNumber ? `<p class="tax-number">الرقم الضريبي: ${companyInfo.taxNumber}</p>` : ''}
                        ${companyInfo.commercialRegister ? `<p class="commercial-register">السجل التجاري: ${companyInfo.commercialRegister}</p>` : ''}
                    </div>
                </div>

                <div class="invoice-info">
                    <h2>فاتورة مبيعات</h2>
                    <div class="invoice-details">
                        <p><strong>رقم الفاتورة:</strong> ${invoiceNumber}</p>
                        <p><strong>التاريخ الميلادي:</strong> ${db.formatDate(sale.date, true)}</p>
                        ${hijriDate ? `<p><strong>التاريخ الهجري:</strong> ${hijriDate}</p>` : ''}
                        <p><strong>الوقت:</strong> ${currentDate.toLocaleTimeString('ar-SA')}</p>
                    </div>
                </div>

                ${customer ? `
                <div class="customer-info">
                    <h3>بيانات العميل</h3>
                    <p><strong>الاسم:</strong> ${customer.name}</p>
                    ${customer.phone ? `<p><strong>الهاتف:</strong> ${customer.phone}</p>` : ''}
                    ${customer.email ? `<p><strong>البريد الإلكتروني:</strong> ${customer.email}</p>` : ''}
                    ${customer.address ? `<p><strong>العنوان:</strong> ${customer.address}</p>` : ''}
                </div>
                ` : ''}

                <div class="items-table">
                    <table>
                        <thead>
                            <tr>
                                <th>م</th>
                                <th>اسم المنتج</th>
                                <th>الكمية</th>
                                <th>سعر الوحدة</th>
                                <th>الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${sale.items.map((item, index) => {
                                const product = db.getProduct(item.productId);
                                const productName = product ? product.name : 'منتج غير معروف';
                                const itemTotal = item.quantity * item.price;

                                return `
                                    <tr>
                                        <td>${index + 1}</td>
                                        <td>${productName}</td>
                                        <td>${item.quantity}</td>
                                        <td>${db.formatCurrency(item.price)}</td>
                                        <td>${db.formatCurrency(itemTotal)}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>

                <div class="invoice-totals">
                    <div class="totals-row">
                        <span class="label">المجموع الفرعي:</span>
                        <span class="value">${db.formatCurrency(sale.subtotal || sale.total)}</span>
                    </div>
                    ${sale.tax && sale.tax > 0 ? `
                    <div class="totals-row">
                        <span class="label">الضريبة:</span>
                        <span class="value">${db.formatCurrency(sale.tax)}</span>
                    </div>
                    ` : ''}
                    ${sale.discount && sale.discount > 0 ? `
                    <div class="totals-row">
                        <span class="label">الخصم:</span>
                        <span class="value">-${db.formatCurrency(sale.discount)}</span>
                    </div>
                    ` : ''}
                    <div class="totals-row total-final">
                        <span class="label">الإجمالي النهائي:</span>
                        <span class="value">${db.formatCurrency(sale.total)}</span>
                    </div>
                </div>

                <div class="payment-info">
                    <p><strong>طريقة الدفع:</strong> ${getPaymentMethodText(sale.paymentMethod)}</p>
                    ${sale.amountPaid && sale.amountPaid !== sale.total ? `
                        <p><strong>المبلغ المدفوع:</strong> ${db.formatCurrency(sale.amountPaid)}</p>
                        ${sale.amountPaid > sale.total ? `<p><strong>الباقي:</strong> ${db.formatCurrency(sale.amountPaid - sale.total)}</p>` : ''}
                    ` : ''}
                </div>

                <div class="invoice-footer">
                    <div class="footer-content">
                        <p class="thank-you">شكراً لتعاملكم معنا</p>
                        <p class="return-policy">سياسة الإرجاع: يمكن إرجاع المنتجات خلال 7 أيام من تاريخ الشراء</p>
                    </div>
                    <div class="print-info">
                        <p>تم الطباعة في: ${new Date().toLocaleString('ar-SA')}</p>
                        <p>نظام تكنوفلاش لإدارة نقاط البيع</p>
                    </div>
                </div>
            </div>
        `;

        // إنشاء نافذة طباعة
        const printWindow = window.open('', '_blank');
        printWindow.document.write(createStandardInvoiceHTML(invoiceContent));
        printWindow.document.close();
        printWindow.print();

        console.log('✅ تم إنشاء الفاتورة العادية بنجاح');

    } catch (error) {
        console.error('❌ خطأ في طباعة الفاتورة العادية:', error);
        app.showAlert('خطأ', 'فشل في طباعة الفاتورة العادية: ' + error.message);
    }
}

/**
 * إنشاء HTML للفاتورة العادية
 */
function createStandardInvoiceHTML(content) {
    return `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة مبيعات</title>
            <style>
                * { box-sizing: border-box; }
                body {
                    font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
                    direction: rtl;
                    margin: 0;
                    padding: 20px;
                    background: #f5f5f5;
                    color: #333;
                }
                .invoice {
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    padding: 30px;
                    border-radius: 8px;
                    box-shadow: 0 0 20px rgba(0,0,0,0.1);
                }
                .invoice-header {
                    text-align: center;
                    border-bottom: 3px solid #2c3e50;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }
                .company-info h1 {
                    color: #2c3e50;
                    margin: 0 0 10px 0;
                    font-size: 2.5em;
                    font-weight: bold;
                }
                .company-info p {
                    margin: 5px 0;
                    color: #666;
                    font-size: 1.1em;
                }
                .invoice-info {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 30px;
                    padding: 20px;
                    background: #f8f9fa;
                    border-radius: 5px;
                }
                .invoice-info h2 {
                    color: #2c3e50;
                    margin: 0;
                    font-size: 1.8em;
                }
                .invoice-details p {
                    margin: 8px 0;
                    font-size: 1.1em;
                }
                .customer-info {
                    background: #e8f4f8;
                    padding: 20px;
                    border-radius: 5px;
                    margin-bottom: 30px;
                }
                .customer-info h3 {
                    color: #2c3e50;
                    margin: 0 0 15px 0;
                    font-size: 1.4em;
                }
                .customer-info p {
                    margin: 8px 0;
                    font-size: 1.1em;
                }
                .items-table {
                    margin-bottom: 30px;
                }
                .items-table table {
                    width: 100%;
                    border-collapse: collapse;
                    font-size: 1.1em;
                }
                .items-table th,
                .items-table td {
                    padding: 12px;
                    text-align: center;
                    border: 1px solid #ddd;
                }
                .items-table th {
                    background: #2c3e50;
                    color: white;
                    font-weight: bold;
                }
                .items-table tbody tr:nth-child(even) {
                    background: #f8f9fa;
                }
                .items-table tbody tr:hover {
                    background: #e8f4f8;
                }
                .invoice-totals {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 5px;
                    margin-bottom: 30px;
                }
                .totals-row {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px 0;
                    font-size: 1.2em;
                }
                .totals-row.total-final {
                    border-top: 2px solid #2c3e50;
                    margin-top: 15px;
                    padding-top: 15px;
                    font-weight: bold;
                    font-size: 1.4em;
                    color: #2c3e50;
                }
                .payment-info {
                    background: #e8f4f8;
                    padding: 20px;
                    border-radius: 5px;
                    margin-bottom: 30px;
                }
                .payment-info p {
                    margin: 8px 0;
                    font-size: 1.2em;
                }
                .invoice-footer {
                    text-align: center;
                    border-top: 2px solid #2c3e50;
                    padding-top: 20px;
                }
                .thank-you {
                    font-size: 1.5em;
                    color: #2c3e50;
                    font-weight: bold;
                    margin-bottom: 15px;
                }
                .return-policy {
                    color: #666;
                    font-size: 1.1em;
                    margin-bottom: 20px;
                }
                .print-info {
                    margin-top: 20px;
                    padding-top: 20px;
                    border-top: 1px solid #ddd;
                    font-size: 0.9em;
                    color: #888;
                }
                .print-info p {
                    margin: 5px 0;
                }
                @media print {
                    body {
                        margin: 0;
                        padding: 0;
                        background: white;
                    }
                    .invoice {
                        border: none;
                        border-radius: 0;
                        box-shadow: none;
                        max-width: none;
                    }
                    @page {
                        margin: 1cm;
                        size: A4;
                    }
                }
            </style>
        </head>
        <body>
            ${content}
        </body>
        </html>
    `;
}

/**
 * طباعة ملصقات متعددة محسنة للمنتجات (حجم صغير)
 */
function printMultipleBarcodeLabels(productIds) {
    try {
        console.log('🏷️ بدء طباعة ملصقات متعددة محسنة...');

        if (!productIds || productIds.length === 0) {
            throw new Error('لم يتم تحديد منتجات للطباعة');
        }

        let allLabelsContent = '';

        // تهيئة الطابعة
        allLabelsContent += ESC_POS_COMMANDS.INIT;
        allLabelsContent += ESC_POS_COMMANDS.RESET;

        productIds.forEach((productId, index) => {
            const product = db.getProduct(productId);
            if (!product || !product.barcode) {
                console.warn(`تم تخطي المنتج ${productId} - لا يحتوي على باركود`);
                return;
            }

            // فاصل بين الملصقات (محسن للحجم الصغير)
            if (index > 0) {
                allLabelsContent += '\n' + repeatChar('-', 20) + '\n';
            }

            // اسم المنتج (محسن للملصق الصغير)
            allLabelsContent += ESC_POS_COMMANDS.ALIGN_CENTER;
            allLabelsContent += ESC_POS_COMMANDS.BOLD_ON;
            const truncatedName = truncateText(product.name, 20); // أقصر للملصق الصغير
            allLabelsContent += truncatedName + '\n';
            allLabelsContent += ESC_POS_COMMANDS.BOLD_OFF;

            // إعدادات الباركود المحسنة للحجم الصغير
            const smallBarcodeHeight = 30; // أقل من الافتراضي
            const smallBarcodeWidth = 1;   // أرفع للوضوح

            allLabelsContent += ESC_POS_COMMANDS.BARCODE_HEIGHT(smallBarcodeHeight);
            allLabelsContent += ESC_POS_COMMANDS.BARCODE_WIDTH(smallBarcodeWidth);
            allLabelsContent += ESC_POS_COMMANDS.BARCODE_POSITION;

            // طباعة الباركود
            if (product.barcode.length === 13 && /^\d+$/.test(product.barcode)) {
                allLabelsContent += ESC_POS_COMMANDS.BARCODE_EAN13;
            } else {
                allLabelsContent += ESC_POS_COMMANDS.BARCODE_CODE128;
            }

            allLabelsContent += String.fromCharCode(product.barcode.length) + product.barcode;
            allLabelsContent += '\n';

            // رقم الباركود
            allLabelsContent += ESC_POS_COMMANDS.ALIGN_CENTER;
            allLabelsContent += product.barcode + '\n';

            // السعر (في النهاية للملصق الصغير)
            if (product.price) {
                allLabelsContent += formatCurrencyForThermal(product.price) + '\n';
            }
        });

        // قطع نهائي
        allLabelsContent += ESC_POS_COMMANDS.FEED_LINES(1);
        allLabelsContent += ESC_POS_COMMANDS.CUT_FEED;

        // إرسال للطباعة
        sendToThermalPrinter(allLabelsContent, 'ملصقات الباركود المحسنة');

        console.log('✅ تم إنشاء الملصقات المتعددة المحسنة بنجاح');

    } catch (error) {
        console.error('❌ خطأ في طباعة الملصقات المتعددة:', error);
        app.showAlert('خطأ', 'فشل في طباعة الملصقات المتعددة: ' + error.message);
    }
}

/**
 * طباعة ملصق باركود بحجم مخصص
 */
function printCustomSizeBarcodeLabel(product, options = {}) {
    try {
        const {
            width = 20,           // عرض النص
            barcodeHeight = 30,   // ارتفاع الباركود
            barcodeWidth = 1,     // عرض خطوط الباركود
            showPrice = true,     // عرض السعر
            showCode = false      // عرض كود المنتج
        } = options;

        let labelContent = '';

        // تهيئة الطابعة
        labelContent += ESC_POS_COMMANDS.INIT;
        labelContent += ESC_POS_COMMANDS.RESET;

        // اسم المنتج
        labelContent += ESC_POS_COMMANDS.ALIGN_CENTER;
        labelContent += ESC_POS_COMMANDS.BOLD_ON;
        const truncatedName = truncateText(product.name, width);
        labelContent += truncatedName + '\n';
        labelContent += ESC_POS_COMMANDS.BOLD_OFF;

        // كود المنتج (اختياري)
        if (showCode && product.code) {
            labelContent += `كود: ${truncateText(product.code, width - 5)}\n`;
        }

        // إعدادات الباركود المخصصة
        labelContent += ESC_POS_COMMANDS.BARCODE_HEIGHT(barcodeHeight);
        labelContent += ESC_POS_COMMANDS.BARCODE_WIDTH(barcodeWidth);
        labelContent += ESC_POS_COMMANDS.BARCODE_POSITION;

        // طباعة الباركود
        if (product.barcode.length === 13 && /^\d+$/.test(product.barcode)) {
            labelContent += ESC_POS_COMMANDS.BARCODE_EAN13;
        } else {
            labelContent += ESC_POS_COMMANDS.BARCODE_CODE128;
        }

        labelContent += String.fromCharCode(product.barcode.length) + product.barcode;
        labelContent += '\n';

        // رقم الباركود
        labelContent += ESC_POS_COMMANDS.ALIGN_CENTER;
        labelContent += product.barcode + '\n';

        // السعر (اختياري)
        if (showPrice && product.price) {
            labelContent += formatCurrencyForThermal(product.price) + '\n';
        }

        // تغذية وقطع
        labelContent += ESC_POS_COMMANDS.FEED_LINES(1);
        labelContent += ESC_POS_COMMANDS.CUT_FEED;

        // إرسال للطباعة
        sendToThermalPrinter(labelContent, 'ملصق باركود مخصص');

        return true;

    } catch (error) {
        throw new Error('فشل في طباعة الملصق المخصص: ' + error.message);
    }
}

/**
 * اختبار شامل للطابعة الحرارية
 */
async function testThermalPrinter() {
    try {
        console.log('🧪 بدء اختبار شامل للطابعة الحرارية...');

        // إظهار رسالة للمستخدم
        if (typeof app !== 'undefined' && app.showNotification) {
            app.showNotification('جاري اختبار الطابعة الحرارية...', 'info');
        }

        let testContent = '';

        // تهيئة الطابعة
        testContent += ESC_POS_COMMANDS.INIT;
        testContent += ESC_POS_COMMANDS.RESET;
        testContent += ESC_POS_COMMANDS.WAKE_UP;

        // عنوان الاختبار
        testContent += ESC_POS_COMMANDS.ALIGN_CENTER;
        testContent += ESC_POS_COMMANDS.DOUBLE_SIZE;
        testContent += ESC_POS_COMMANDS.BOLD_ON;
        testContent += 'اختبار الطابعة\n';
        testContent += ESC_POS_COMMANDS.NORMAL_SIZE;
        testContent += ESC_POS_COMMANDS.BOLD_OFF;

        testContent += repeatChar('=', 32) + '\n';

        // معلومات النظام
        testContent += ESC_POS_COMMANDS.ALIGN_LEFT;
        testContent += `نظام: تكنوفلاش POS\n`;
        testContent += `التاريخ: ${new Date().toLocaleDateString('ar-SA')}\n`;
        testContent += `الوقت: ${new Date().toLocaleTimeString('ar-SA')}\n`;
        testContent += repeatChar('-', 32) + '\n';

        // اختبار الخطوط والأحجام
        testContent += ESC_POS_COMMANDS.BOLD_ON;
        testContent += 'اختبار الخطوط:\n';
        testContent += ESC_POS_COMMANDS.BOLD_OFF;

        testContent += 'خط عادي - Normal Font\n';
        testContent += ESC_POS_COMMANDS.BOLD_ON;
        testContent += 'خط عريض - Bold Font\n';
        testContent += ESC_POS_COMMANDS.BOLD_OFF;

        testContent += ESC_POS_COMMANDS.UNDERLINE_ON;
        testContent += 'اختبار الخط المسطر\n';
        testContent += ESC_POS_COMMANDS.UNDERLINE_OFF;

        // اختبار المحاذاة
        testContent += ESC_POS_COMMANDS.ALIGN_LEFT;
        testContent += 'محاذاة يسار\n';
        testContent += ESC_POS_COMMANDS.ALIGN_CENTER;
        testContent += 'محاذاة وسط\n';
        testContent += ESC_POS_COMMANDS.ALIGN_RIGHT;
        testContent += 'محاذاة يمين\n';

        testContent += ESC_POS_COMMANDS.ALIGN_CENTER;
        testContent += '================================\n';

        // اختبار الأرقام والعملة
        testContent += ESC_POS_COMMANDS.ALIGN_LEFT;
        testContent += 'اختبار الأرقام: ١٢٣٤٥٦٧٨٩٠\n';
        testContent += 'اختبار العملة: ١٢٣.٤٥ ج.م\n';

        // معلومات التاريخ والوقت
        const now = new Date();
        testContent += `التاريخ: ${formatDateForThermal(now)}\n`;
        testContent += `الوقت: ${now.toLocaleTimeString('ar-SA')}\n`;

        testContent += '================================\n';
        testContent += ESC_POS_COMMANDS.ALIGN_CENTER;
        testContent += 'تم الاختبار بنجاح\n';
        testContent += 'نظام تكنوفلاش POS\n';

        // تغذية وقطع
        testContent += '\n\n';
        testContent += ESC_POS_COMMANDS.CUT_FEED;

        // إرسال للطباعة
        sendToThermalPrinter(testContent, 'اختبار الطابعة');

        console.log('✅ تم إرسال اختبار الطابعة');

    } catch (error) {
        console.error('❌ خطأ في اختبار الطابعة:', error);
        app.showAlert('خطأ', 'فشل في اختبار الطابعة: ' + error.message);
    }
}

/**
 * إعدادات الطابعة الحرارية
 */
function configureThermalPrinter(settings = {}) {
    try {
        console.log('⚙️ تكوين إعدادات الطابعة الحرارية...');

        // دمج الإعدادات الجديدة مع الافتراضية
        const config = {
            ...THERMAL_SETTINGS,
            ...settings
        };

        // حفظ الإعدادات
        localStorage.setItem('thermal_printer_settings', JSON.stringify(config));

        // تطبيق الإعدادات على المتغير العام
        Object.assign(THERMAL_SETTINGS, config);

        console.log('✅ تم تكوين إعدادات الطابعة:', config);

        return config;

    } catch (error) {
        console.error('❌ خطأ في تكوين الطابعة:', error);
        return THERMAL_SETTINGS;
    }
}

/**
 * تحميل إعدادات الطابعة المحفوظة
 */
function loadThermalPrinterSettings() {
    try {
        const savedSettings = localStorage.getItem('thermal_printer_settings');
        if (savedSettings) {
            const config = JSON.parse(savedSettings);
            Object.assign(THERMAL_SETTINGS, config);
            console.log('✅ تم تحميل إعدادات الطابعة المحفوظة:', config);
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل إعدادات الطابعة:', error);
    }
}

// تحميل الإعدادات عند بدء التشغيل
document.addEventListener('DOMContentLoaded', () => {
    loadThermalPrinterSettings();
});

/**
 * إنشاء HTML للفاتورة العادية
 */
function createStandardInvoiceHTML(content) {
    return `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة مبيعات</title>
            <style>
                * { box-sizing: border-box; }
                body {
                    font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
                    direction: rtl;
                    margin: 0;
                    padding: 20px;
                    background: #f5f5f5;
                    color: #333;
                }
                .invoice {
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    padding: 30px;
                    border-radius: 8px;
                    box-shadow: 0 0 20px rgba(0,0,0,0.1);
                }
                .invoice-header {
                    text-align: center;
                    border-bottom: 3px solid #2c3e50;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }
                .company-info h1 {
                    color: #2c3e50;
                    margin: 0 0 10px 0;
                    font-size: 2.5em;
                    font-weight: bold;
                }
                .company-info p {
                    margin: 5px 0;
                    color: #666;
                    font-size: 1.1em;
                }
                .invoice-info {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 30px;
                    padding: 20px;
                    background: #f8f9fa;
                    border-radius: 5px;
                }
                .invoice-info h2 {
                    color: #2c3e50;
                    margin: 0;
                    font-size: 1.8em;
                }
                .invoice-details p {
                    margin: 8px 0;
                    font-size: 1.1em;
                }
                .customer-info {
                    background: #e8f4f8;
                    padding: 20px;
                    border-radius: 5px;
                    margin-bottom: 30px;
                }
                .customer-info h3 {
                    color: #2c3e50;
                    margin: 0 0 15px 0;
                    font-size: 1.4em;
                }
                .customer-info p {
                    margin: 8px 0;
                    font-size: 1.1em;
                }
                .items-table {
                    margin-bottom: 30px;
                }
                .items-table table {
                    width: 100%;
                    border-collapse: collapse;
                    font-size: 1.1em;
                }
                .items-table th,
                .items-table td {
                    padding: 12px;
                    text-align: center;
                    border: 1px solid #ddd;
                }
                .items-table th {
                    background: #2c3e50;
                    color: white;
                    font-weight: bold;
                }
                .items-table tbody tr:nth-child(even) {
                    background: #f8f9fa;
                }
                .items-table tbody tr:hover {
                    background: #e8f4f8;
                }
                .invoice-totals {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 5px;
                    margin-bottom: 30px;
                }
                .totals-row {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px 0;
                    font-size: 1.2em;
                }
                .totals-row.total-final {
                    border-top: 2px solid #2c3e50;
                    margin-top: 15px;
                    padding-top: 15px;
                    font-weight: bold;
                    font-size: 1.4em;
                    color: #2c3e50;
                }
                .payment-info {
                    background: #e8f4f8;
                    padding: 20px;
                    border-radius: 5px;
                    margin-bottom: 30px;
                }
                .payment-info p {
                    margin: 8px 0;
                    font-size: 1.2em;
                }
                .invoice-footer {
                    text-align: center;
                    border-top: 2px solid #2c3e50;
                    padding-top: 20px;
                }
                .thank-you {
                    font-size: 1.5em;
                    color: #2c3e50;
                    font-weight: bold;
                    margin-bottom: 15px;
                }
                .return-policy {
                    color: #666;
                    font-size: 1.1em;
                    margin-bottom: 20px;
                }
                .print-info {
                    margin-top: 20px;
                    padding-top: 20px;
                    border-top: 1px solid #ddd;
                    font-size: 0.9em;
                    color: #888;
                }
                .print-info p {
                    margin: 5px 0;
                }
                @media print {
                    body {
                        margin: 0;
                        padding: 0;
                        background: white;
                    }
                    .invoice {
                        border: none;
                        border-radius: 0;
                        box-shadow: none;
                        max-width: none;
                    }
                    @page {
                        margin: 1cm;
                        size: A4;
                    }
                }
            </style>
        </head>
        <body>
            ${content}
        </body>
        </html>
    `;
}

/**
 * طباعة ملصقات متعددة للمنتجات
 */
function printMultipleBarcodeLabels(productIds) {
    try {
        console.log('🏷️ بدء طباعة ملصقات متعددة...');

        if (!productIds || productIds.length === 0) {
            throw new Error('لم يتم تحديد منتجات للطباعة');
        }

        let allLabelsContent = '';

        // تهيئة الطابعة
        allLabelsContent += ESC_POS_COMMANDS.INIT;

        productIds.forEach((productId, index) => {
            const product = db.getProduct(productId);
            if (!product || !product.barcode) {
                console.warn(`تم تخطي المنتج ${productId} - لا يحتوي على باركود`);
                return;
            }

            // فاصل بين الملصقات
            if (index > 0) {
                allLabelsContent += '\n' + '='.repeat(32) + '\n';
            }

            // اسم المنتج
            allLabelsContent += ESC_POS_COMMANDS.ALIGN_CENTER;
            allLabelsContent += ESC_POS_COMMANDS.BOLD_ON;
            const truncatedName = truncateText(product.name, 24);
            allLabelsContent += truncatedName + '\n';
            allLabelsContent += ESC_POS_COMMANDS.BOLD_OFF;

            // كود المنتج إن وجد
            if (product.code) {
                allLabelsContent += `كود: ${product.code}\n`;
            }

            // السعر
            allLabelsContent += ESC_POS_COMMANDS.BOLD_ON;
            allLabelsContent += `السعر: ${formatCurrencyForThermal(product.price)}\n`;
            allLabelsContent += ESC_POS_COMMANDS.BOLD_OFF;

            // إعدادات الباركود
            allLabelsContent += ESC_POS_COMMANDS.BARCODE_HEIGHT(THERMAL_SETTINGS.BARCODE_HEIGHT);
            allLabelsContent += ESC_POS_COMMANDS.BARCODE_WIDTH(THERMAL_SETTINGS.BARCODE_WIDTH);
            allLabelsContent += ESC_POS_COMMANDS.BARCODE_POSITION;

            // طباعة الباركود
            if (product.barcode.length === 13 && /^\d+$/.test(product.barcode)) {
                allLabelsContent += ESC_POS_COMMANDS.BARCODE_EAN13;
            } else {
                allLabelsContent += ESC_POS_COMMANDS.BARCODE_CODE128;
            }

            allLabelsContent += String.fromCharCode(product.barcode.length) + product.barcode;
            allLabelsContent += '\n\n';

            // رقم الباركود
            allLabelsContent += ESC_POS_COMMANDS.ALIGN_CENTER;
            allLabelsContent += product.barcode + '\n\n';
        });

        // قطع نهائي
        allLabelsContent += ESC_POS_COMMANDS.CUT_FEED;

        // إرسال للطباعة
        sendToThermalPrinter(allLabelsContent, 'ملصقات الباركود');

        console.log('✅ تم إنشاء الملصقات المتعددة بنجاح');

    } catch (error) {
        console.error('❌ خطأ في طباعة الملصقات المتعددة:', error);
        app.showAlert('خطأ', 'فشل في طباعة الملصقات المتعددة: ' + error.message);
    }
}
