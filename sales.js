/**
 * إدارة المبيعات - تكنوفلاش
 */

// مستمع أحداث تحديث الإعدادات
window.addEventListener('settingsUpdated', function(event) {
    const settings = event.detail;
    console.log('تم استلام تحديث الإعدادات في المبيعات:', settings);

    // تحديث الوضع الليلي
    if (settings.general && typeof settings.general.darkMode !== 'undefined') {
        if (settings.general.darkMode) {
            document.body.classList.add('dark-mode');
        } else {
            document.body.classList.remove('dark-mode');
        }
        console.log('تم تحديث الوضع الليلي في المبيعات:', settings.general.darkMode);
    }

    // تحديث العملة في واجهة المبيعات
    if (settings.general && settings.general.currency) {
        updateSalesCurrency(settings.general.currency);
    }

    // تحديث نوع الأرقام
    if (settings.general && settings.general.numberType) {
        updateSalesNumbers(settings.general.numberType);
    }

    // تحديث بيانات الشركة
    if (settings.company) {
        updateCompanyInfo(settings.company);
    }

    // تحديث إعدادات نقطة البيع
    if (settings.pos) {
        updatePOSSettings(settings.pos);
    }
});

let currentSale = {
    items: [],
    customerId: 'guest',
    discount: 0,
    tax: 0,
    paymentMethod: 'cash',
    paidAmount: 0,
    notes: ''
};

/**
 * تحميل صفحة المبيعات
 */
async function loadSales() {
    const mainContent = document.getElementById('mainContent');
    
    mainContent.innerHTML = `
        <div class="page-header">
            <h1><i class="fas fa-shopping-cart"></i> نقطة البيع</h1>
            <div class="page-actions">
                <button class="btn btn-success" onclick="completeSale()" id="completeSaleBtn" disabled>
                    <i class="fas fa-check"></i> إتمام البيع
                </button>
                <button class="btn btn-warning" onclick="clearSale()">
                    <i class="fas fa-trash"></i> مسح الكل
                </button>
                <button class="btn btn-info" onclick="showSalesHistory()">
                    <i class="fas fa-history"></i> سجل المبيعات
                </button>
            </div>
        </div>

        <div class="sales-container">
            <div class="row">
                <!-- قسم إضافة المنتجات -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-barcode"></i>
                                إضافة منتج
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- شريط البحث والباركود -->
                            <div class="search-section">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>البحث بالاسم أو الباركود</label>
                                        <div class="search-bar">
                                            <input type="text" class="search-input" id="productSearch" 
                                                   placeholder="ابحث عن منتج أو امسح الباركود..." 
                                                   autocomplete="off">
                                            <i class="fas fa-search search-icon"></i>
                                        </div>
                                        <div id="searchResults" class="search-results"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- قائمة المنتجات المضافة -->
                            <div class="sale-items">
                                <h4>المنتجات المضافة</h4>
                                <div class="table-container">
                                    <table class="table" id="saleItemsTable">
                                        <thead>
                                            <tr>
                                                <th>المنتج</th>
                                                <th>السعر</th>
                                                <th>الكمية</th>
                                                <th>الإجمالي</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="no-items">
                                                <td colspan="5" class="text-center">لم يتم إضافة أي منتجات بعد</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قسم تفاصيل البيع -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-calculator"></i>
                                تفاصيل البيع
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- اختيار العميل -->
                            <div class="form-group">
                                <label>العميل</label>
                                <select class="form-control" id="customerSelect">
                                    <option value="guest">عميل عادي</option>
                                </select>
                            </div>

                            <!-- الخصم والضريبة -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label>الخصم (%)</label>
                                    <input type="number" class="form-control" id="discountInput" 
                                           value="0" min="0" max="100" step="0.1">
                                </div>
                                <div class="form-group">
                                    <label>الضريبة (%)</label>
                                    <input type="number" class="form-control" id="taxInput" 
                                           value="0" min="0" max="100" step="0.1">
                                </div>
                            </div>

                            <!-- طريقة الدفع -->
                            <div class="form-group">
                                <label>طريقة الدفع</label>
                                <select class="form-control" id="paymentMethodSelect">
                                    <option value="cash">نقداً</option>
                                    <option value="credit">آجل</option>
                                    <option value="card">بطاقة</option>
                                </select>
                            </div>

                            <!-- المبلغ المدفوع -->
                            <div class="form-group" id="paidAmountGroup">
                                <label>المبلغ المدفوع</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="paidAmountInput"
                                           step="0.01" min="0" placeholder="أدخل المبلغ المدفوع">
                                    <div class="input-group-append">
                                        <span class="input-group-text">ج.م</span>
                                    </div>
                                </div>
                                <small class="form-text text-muted" id="remainingAmountText"></small>
                            </div>

                            <!-- ملاحظات -->
                            <div class="form-group">
                                <label>ملاحظات</label>
                                <textarea class="form-control" id="notesInput" rows="3" 
                                          placeholder="ملاحظات إضافية..."></textarea>
                            </div>

                            <!-- ملخص المبلغ -->
                            <div class="sale-summary">
                                <div class="summary-row">
                                    <span>المجموع الفرعي:</span>
                                    <span id="subtotalAmount">٠.٠٠ ر.س</span>
                                </div>
                                <div class="summary-row">
                                    <span>الخصم:</span>
                                    <span id="discountAmount">٠.٠٠ ر.س</span>
                                </div>
                                <div class="summary-row">
                                    <span>الضريبة:</span>
                                    <span id="taxAmount">٠.٠٠ ر.س</span>
                                </div>
                                <div class="summary-row total">
                                    <span>الإجمالي:</span>
                                    <span id="totalAmount">٠.٠٠ ر.س</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة سجل المبيعات -->
        <div id="salesHistoryModal" class="modal hidden">
            <div class="modal-content large">
                <div class="modal-header">
                    <h3>سجل المبيعات</h3>
                    <button class="modal-close" onclick="app.hideModal('salesHistoryModal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="table-container">
                        <table class="table" id="salesHistoryTable">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>طريقة الدفع</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    `;

    // تهيئة الصفحة
    await initializeSalesPage();
}

/**
 * تهيئة صفحة المبيعات
 */
async function initializeSalesPage() {
    // تحميل العملاء
    loadCustomersSelect();
    
    // إعداد مستمعي الأحداث
    setupSalesEventListeners();

    // تحديث الملخص
    updateSaleSummary();
    updatePaidAmountVisibility();
}

/**
 * تحميل قائمة العملاء
 */
function loadCustomersSelect() {
    const customers = db.getCustomers();
    const customerSelect = document.getElementById('customerSelect');
    
    customerSelect.innerHTML = '<option value="guest">عميل عادي</option>';
    
    customers.forEach(customer => {
        if (customer.id !== 'guest') {
            const option = document.createElement('option');
            option.value = customer.id;
            option.textContent = customer.name;
            customerSelect.appendChild(option);
        }
    });
}

/**
 * إعداد مستمعي الأحداث
 */
function setupSalesEventListeners() {
    // البحث عن المنتجات
    const productSearch = document.getElementById('productSearch');
    productSearch.addEventListener('input', handleProductSearch);
    productSearch.addEventListener('keydown', handleSearchKeydown);
    
    // تغيير العميل
    document.getElementById('customerSelect').addEventListener('change', (e) => {
        currentSale.customerId = e.target.value;
    });
    
    // تغيير الخصم والضريبة
    document.getElementById('discountInput').addEventListener('input', (e) => {
        currentSale.discount = parseFloat(e.target.value) || 0;
        updateSaleSummary();
    });
    
    document.getElementById('taxInput').addEventListener('input', (e) => {
        currentSale.tax = parseFloat(e.target.value) || 0;
        updateSaleSummary();
    });
    
    // تغيير طريقة الدفع
    document.getElementById('paymentMethodSelect').addEventListener('change', (e) => {
        currentSale.paymentMethod = e.target.value;
        updatePaidAmountVisibility();
        updateSaleSummary();
    });

    // تغيير المبلغ المدفوع
    document.getElementById('paidAmountInput').addEventListener('input', (e) => {
        currentSale.paidAmount = parseFloat(e.target.value) || 0;
        updateSaleSummary();
    });

    // تغيير الملاحظات
    document.getElementById('notesInput').addEventListener('input', (e) => {
        currentSale.notes = e.target.value;
    });

    // تحميل وتطبيق الإعدادات
    try {
        const settings = JSON.parse(localStorage.getItem('technoflash_settings') || '{}');

        // تطبيق الإعدادات العامة
        if (settings.general) {
            if (settings.general.darkMode) {
                document.body.classList.add('dark-mode');
            }
            if (settings.general.numberType) {
                document.documentElement.setAttribute('data-number-type', settings.general.numberType);
            }
            if (settings.general.currency) {
                document.documentElement.setAttribute('data-currency', settings.general.currency);
            }
        }

        // تطبيق إعدادات نقطة البيع
        if (settings.pos) {
            updatePOSSettings(settings.pos);
        }

        // تطبيق بيانات الشركة
        if (settings.company) {
            updateCompanyInfo(settings.company);
        }
    } catch (error) {
        console.error('خطأ في تحميل الإعدادات في المبيعات:', error);
    }
}

/**
 * معالجة البحث عن المنتجات
 */
function handleProductSearch(e) {
    const query = e.target.value.trim();
    const searchResults = document.getElementById('searchResults');
    
    if (query.length < 2) {
        searchResults.innerHTML = '';
        searchResults.style.display = 'none';
        return;
    }
    
    const products = db.searchProducts(query);
    
    if (products.length === 0) {
        searchResults.innerHTML = '<div class="search-result-item no-results">لا توجد نتائج</div>';
        searchResults.style.display = 'block';
        return;
    }
    
    searchResults.innerHTML = products.map(product => `
        <div class="search-result-item" onclick="addProductToSale('${product.id}')">
            <div class="product-info">
                <strong>${product.name}</strong>
                <br>
                <small>الباركود: ${product.barcode || 'غير محدد'}</small>
                <br>
                <small>المخزون: ${db.toArabicNumerals(product.quantity)}</small>
            </div>
            <div class="product-price">
                ${db.formatCurrency(product.price)}
            </div>
        </div>
    `).join('');
    
    searchResults.style.display = 'block';
}

/**
 * معالجة ضغط المفاتيح في البحث
 */
function handleSearchKeydown(e) {
    if (e.key === 'Enter') {
        e.preventDefault();
        const query = e.target.value.trim();
        
        if (query) {
            // البحث بالباركود أولاً
            const product = db.getProductByBarcode(query);
            if (product) {
                addProductToSale(product.id);
                e.target.value = '';
                document.getElementById('searchResults').style.display = 'none';
            } else {
                // البحث بالاسم
                const products = db.searchProducts(query);
                if (products.length === 1) {
                    addProductToSale(products[0].id);
                    e.target.value = '';
                    document.getElementById('searchResults').style.display = 'none';
                }
            }
        }
    }
}

/**
 * إضافة منتج إلى البيع
 */
function addProductToSale(productId, quantity = 1) {
    const product = db.getProduct(productId);
    
    if (!product) {
        app.showAlert('خطأ', 'المنتج غير موجود');
        return;
    }
    
    if (product.quantity < quantity) {
        app.showAlert('تنبيه', `المخزون المتاح: ${db.toArabicNumerals(product.quantity)} فقط`);
        return;
    }
    
    // البحث عن المنتج في القائمة الحالية
    const existingItemIndex = currentSale.items.findIndex(item => item.productId === productId);
    
    if (existingItemIndex >= 0) {
        // زيادة الكمية
        const newQuantity = currentSale.items[existingItemIndex].quantity + quantity;
        if (product.quantity < newQuantity) {
            app.showAlert('تنبيه', `المخزون المتاح: ${db.toArabicNumerals(product.quantity)} فقط`);
            return;
        }
        currentSale.items[existingItemIndex].quantity = newQuantity;
        currentSale.items[existingItemIndex].total = newQuantity * product.price;
    } else {
        // إضافة منتج جديد
        currentSale.items.push({
            productId: productId,
            name: product.name,
            price: product.price,
            quantity: quantity,
            total: quantity * product.price
        });
    }
    
    // تحديث العرض
    updateSaleItemsTable();
    updateSaleSummary();
    
    // مسح البحث
    document.getElementById('productSearch').value = '';
    document.getElementById('searchResults').style.display = 'none';
    
    app.showNotification(`تم إضافة ${product.name}`, 'success', 2000);
}

/**
 * تحديث جدول المنتجات المضافة
 */
function updateSaleItemsTable() {
    const tbody = document.querySelector('#saleItemsTable tbody');
    
    if (currentSale.items.length === 0) {
        tbody.innerHTML = `
            <tr class="no-items">
                <td colspan="5" class="text-center">لم يتم إضافة أي منتجات بعد</td>
            </tr>
        `;
        document.getElementById('completeSaleBtn').disabled = true;
        return;
    }
    
    tbody.innerHTML = currentSale.items.map((item, index) => `
        <tr>
            <td>${item.name}</td>
            <td>${db.formatCurrency(item.price)}</td>
            <td>
                <div class="quantity-controls">
                    <button class="btn btn-sm btn-secondary" onclick="changeItemQuantity(${index}, -1)">
                        <i class="fas fa-minus"></i>
                    </button>
                    <span class="quantity-value">${db.toArabicNumerals(item.quantity)}</span>
                    <button class="btn btn-sm btn-secondary" onclick="changeItemQuantity(${index}, 1)">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </td>
            <td>${db.formatCurrency(item.total)}</td>
            <td>
                <button class="btn btn-sm btn-danger" onclick="removeItemFromSale(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
    
    document.getElementById('completeSaleBtn').disabled = false;
}

/**
 * تغيير كمية منتج
 */
function changeItemQuantity(itemIndex, change) {
    const item = currentSale.items[itemIndex];
    const product = db.getProduct(item.productId);
    const newQuantity = item.quantity + change;
    
    if (newQuantity <= 0) {
        removeItemFromSale(itemIndex);
        return;
    }
    
    if (newQuantity > product.quantity) {
        app.showAlert('تنبيه', `المخزون المتاح: ${db.toArabicNumerals(product.quantity)} فقط`);
        return;
    }
    
    item.quantity = newQuantity;
    item.total = newQuantity * item.price;
    
    updateSaleItemsTable();
    updateSaleSummary();
}

/**
 * إزالة منتج من البيع
 */
function removeItemFromSale(itemIndex) {
    currentSale.items.splice(itemIndex, 1);
    updateSaleItemsTable();
    updateSaleSummary();
    
    app.showNotification('تم حذف المنتج', 'info', 2000);
}

/**
 * تحديث ملخص البيع
 */
function updateSaleSummary() {
    const subtotal = currentSale.items.reduce((sum, item) => sum + item.total, 0);
    const discountAmount = (subtotal * currentSale.discount) / 100;
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = (taxableAmount * currentSale.tax) / 100;
    const total = taxableAmount + taxAmount;

    document.getElementById('subtotalAmount').textContent = db.formatCurrency(subtotal);
    document.getElementById('discountAmount').textContent = db.formatCurrency(discountAmount);
    document.getElementById('taxAmount').textContent = db.formatCurrency(taxAmount);
    document.getElementById('totalAmount').textContent = db.formatCurrency(total);

    // تحديث المبلغ المدفوع حسب طريقة الدفع
    updatePaidAmountField(total);
}

/**
 * تحديث حقل المبلغ المدفوع
 */
function updatePaidAmountField(total) {
    const paidAmountInput = document.getElementById('paidAmountInput');
    const remainingAmountText = document.getElementById('remainingAmountText');

    if (currentSale.paymentMethod === 'cash') {
        // نقداً - المبلغ المدفوع = الإجمالي
        currentSale.paidAmount = total;
        paidAmountInput.value = total;
        remainingAmountText.textContent = '';
    } else {
        // آجل أو بطاقة - يمكن تعديل المبلغ المدفوع
        if (currentSale.paidAmount === 0) {
            currentSale.paidAmount = currentSale.paymentMethod === 'credit' ? 0 : total;
            paidAmountInput.value = currentSale.paidAmount;
        }

        const remaining = total - currentSale.paidAmount;
        if (remaining > 0) {
            remainingAmountText.textContent = `المبلغ المتبقي: ${db.formatCurrency(remaining)}`;
            remainingAmountText.style.color = '#dc3545';
        } else if (remaining < 0) {
            remainingAmountText.textContent = `مبلغ زائد: ${db.formatCurrency(Math.abs(remaining))}`;
            remainingAmountText.style.color = '#ffc107';
        } else {
            remainingAmountText.textContent = 'مدفوع بالكامل';
            remainingAmountText.style.color = '#28a745';
        }
    }
}

/**
 * تحديث إظهار حقل المبلغ المدفوع
 */
function updatePaidAmountVisibility() {
    const paidAmountGroup = document.getElementById('paidAmountGroup');
    const paidAmountInput = document.getElementById('paidAmountInput');

    if (currentSale.paymentMethod === 'cash') {
        // نقداً - إخفاء الحقل أو جعله للقراءة فقط
        paidAmountInput.readOnly = true;
        paidAmountInput.style.backgroundColor = '#f8f9fa';
    } else {
        // آجل أو بطاقة - إظهار الحقل للتعديل
        paidAmountInput.readOnly = false;
        paidAmountInput.style.backgroundColor = '';
    }
}

/**
 * مسح البيع الحالي
 */
function clearSale() {
    if (currentSale.items.length === 0) {
        return;
    }
    
    app.showConfirm('مسح البيع', 'هل أنت متأكد من مسح جميع المنتجات؟', () => {
        currentSale = {
            items: [],
            customerId: 'guest',
            discount: 0,
            tax: 0,
            paymentMethod: 'cash',
            paidAmount: 0,
            notes: ''
        };

        // إعادة تعيين النموذج
        document.getElementById('customerSelect').value = 'guest';
        document.getElementById('discountInput').value = '0';
        document.getElementById('taxInput').value = '0';
        document.getElementById('paymentMethodSelect').value = 'cash';
        document.getElementById('paidAmountInput').value = '0';
        document.getElementById('notesInput').value = '';
        
        updateSaleItemsTable();
        updateSaleSummary();
        
        app.showNotification('تم مسح البيع', 'info');
    });
}

/**
 * إتمام البيع مع معالجة محسنة للأخطاء
 */
async function completeSale() {
    if (currentSale.items.length === 0) {
        app.showAlert('خطأ', 'يجب إضافة منتجات أولاً');
        return;
    }

    app.showLoading();

    try {
        console.log('🛒 بدء إتمام البيع...', currentSale);

        // التحقق من المخزون مرة أخيرة مع تفاصيل أكثر
        for (const item of currentSale.items) {
            const product = db.getProduct(item.productId);
            if (!product) {
                throw new Error(`لم يتم العثور على المنتج: ${item.name}`);
            }

            // التحقق من المخزون حسب نوع المنتج
            if (product.isCartonBased) {
                const totalAvailable = (product.cartonQuantity * product.unitsPerCarton) + product.unitQuantity;
                if (totalAvailable < item.quantity) {
                    throw new Error(`المخزون غير كافي للمنتج: ${item.name}. المتاح: ${totalAvailable}, المطلوب: ${item.quantity}`);
                }
            } else {
                if (product.quantity < item.quantity) {
                    throw new Error(`المخزون غير كافي للمنتج: ${item.name}. المتاح: ${product.quantity}, المطلوب: ${item.quantity}`);
                }
            }
        }

        // حساب المبالغ
        const subtotal = currentSale.items.reduce((sum, item) => sum + item.total, 0);
        const discountAmount = (subtotal * currentSale.discount) / 100;
        const taxableAmount = subtotal - discountAmount;
        const taxAmount = (taxableAmount * currentSale.tax) / 100;
        const total = taxableAmount + taxAmount;

        console.log('💰 حساب المبالغ:', { subtotal, discountAmount, taxAmount, total });

        // إنشاء بيانات البيع
        const saleData = {
            customerId: currentSale.customerId,
            items: currentSale.items.map(item => ({
                ...item,
                saleType: item.saleType || 'unit' // إضافة نوع البيع إذا لم يكن موجود
            })),
            subtotal: subtotal,
            discount: currentSale.discount,
            discountAmount: discountAmount,
            tax: currentSale.tax,
            taxAmount: taxAmount,
            total: total,
            paymentMethod: currentSale.paymentMethod,
            amountPaid: currentSale.paidAmount || total, // تصحيح اسم الحقل
            notes: currentSale.notes,
            date: new Date().toISOString()
        };

        console.log('📄 بيانات البيع:', saleData);

        // محاولة حفظ البيع مع معالجة أفضل للأخطاء
        let savedSale;
        try {
            savedSale = db.addSale(saleData);
        } catch (dbError) {
            console.error('❌ خطأ في قاعدة البيانات:', dbError);

            if (dbError.name === 'QuotaExceededError') {
                throw new Error('مساحة التخزين ممتلئة. يرجى حذف بعض البيانات القديمة أو تصدير البيانات.');
            } else {
                throw new Error('فشل في حفظ البيع في قاعدة البيانات: ' + dbError.message);
            }
        }

        if (!savedSale) {
            throw new Error('فشل في حفظ البيع - لم يتم إرجاع بيانات البيع');
        }

        console.log('✅ تم حفظ البيع بنجاح:', savedSale);

        app.showNotification('تم إتمام البيع بنجاح', 'success');

        // عرض خيارات الطباعة
        try {
            showPrintOptions(savedSale);
        } catch (printError) {
            console.error('⚠️ خطأ في عرض خيارات الطباعة:', printError);
            // لا نوقف العملية بسبب خطأ في الطباعة
        }

        // مسح البيع الحالي
        clearSale();

        // تحديث العرض
        if (typeof updateDashboard === 'function') {
            updateDashboard();
        }

    } catch (error) {
        console.error('❌ خطأ في إتمام البيع:', error);

        // عرض رسالة خطأ مفصلة للمستخدم
        let errorMessage = 'حدث خطأ في إتمام البيع';
        if (error.message) {
            errorMessage += ':\n' + error.message;
        }

        app.showAlert('خطأ', errorMessage);
    }

    app.hideLoading();
}

/**
 * عرض خيارات الطباعة بعد إتمام البيع مع معالجة محسنة للأخطاء
 */
function showPrintOptions(saleData) {
    try {
        console.log('🖨️ عرض خيارات الطباعة للبيع:', saleData.id);

        if (!saleData || !saleData.id) {
            throw new Error('بيانات البيع غير صحيحة');
        }

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'printOptionsModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-print"></i> خيارات الطباعة
                        </h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            تم إتمام البيع بنجاح! اختر نوع الطباعة المطلوب:
                        </div>
                        <div class="print-options">
                            <div class="print-option">
                                <div class="option-icon">
                                    <i class="fas fa-receipt"></i>
                                </div>
                                <div class="option-content">
                                    <h6>فاتورة حرارية (58mm)</h6>
                                    <p>مناسبة للطابعات الحرارية الصغيرة</p>
                                    <button class="btn btn-primary btn-sm" onclick="safePrintThermalInvoice('${saleData.id}')">
                                        <i class="fas fa-print"></i> طباعة حرارية
                                    </button>
                                </div>
                            </div>

                            <div class="print-option">
                                <div class="option-icon">
                                    <i class="fas fa-file-invoice"></i>
                                </div>
                                <div class="option-content">
                                    <h6>فاتورة عادية (A4)</h6>
                                    <p>فاتورة مفصلة للطباعة العادية</p>
                                    <button class="btn btn-info btn-sm" onclick="safePrintStandardInvoice('${saleData.id}')">
                                        <i class="fas fa-print"></i> طباعة عادية
                                    </button>
                                </div>
                            </div>

                            <div class="print-option">
                                <div class="option-icon">
                                    <i class="fas fa-barcode"></i>
                                </div>
                                <div class="option-content">
                                    <h6>ملصقات الباركود</h6>
                                    <p>طباعة ملصقات للمنتجات المباعة</p>
                                    <button class="btn btn-success btn-sm" onclick="safePrintSaleItemsLabels('${saleData.id}')">
                                        <i class="fas fa-tags"></i> طباعة الملصقات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                        <button type="button" class="btn btn-warning" onclick="safeTestThermalPrinter()">
                            <i class="fas fa-cog"></i> اختبار الطابعة
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // التحقق من وجود jQuery و Bootstrap
        if (typeof $ !== 'undefined' && $.fn.modal) {
            $('#printOptionsModal').modal('show');

            // إزالة المودال عند الإغلاق
            $('#printOptionsModal').on('hidden.bs.modal', function() {
                if (document.body.contains(modal)) {
                    document.body.removeChild(modal);
                }
            });
        } else {
            console.error('❌ jQuery أو Bootstrap غير متوفر');
            // عرض بديل بدون Bootstrap
            modal.style.display = 'block';
            modal.style.position = 'fixed';
            modal.style.top = '50%';
            modal.style.left = '50%';
            modal.style.transform = 'translate(-50%, -50%)';
            modal.style.zIndex = '9999';
            modal.style.backgroundColor = 'white';
            modal.style.border = '1px solid #ccc';
            modal.style.borderRadius = '8px';
            modal.style.padding = '20px';
            modal.style.boxShadow = '0 4px 6px rgba(0,0,0,0.1)';
        }

    } catch (error) {
        console.error('❌ خطأ في عرض خيارات الطباعة:', error);
        app.showAlert('خطأ', 'فشل في عرض خيارات الطباعة: ' + error.message);
    }
}

/**
 * طباعة ملصقات المنتجات المباعة
 */
function printSaleItemsLabels(saleId) {
    try {
        const sale = db.getSale(saleId);
        if (!sale) {
            throw new Error('لم يتم العثور على بيانات البيع');
        }

        // جمع معرفات المنتجات
        const productIds = sale.items.map(item => item.productId);

        // طباعة الملصقات
        printMultipleBarcodeLabels(productIds);

        // إغلاق مودال خيارات الطباعة
        $('#printOptionsModal').modal('hide');

    } catch (error) {
        console.error('خطأ في طباعة ملصقات المنتجات:', error);
        app.showAlert('خطأ', 'فشل في طباعة ملصقات المنتجات: ' + error.message);
    }
}

/**
 * تحديث أزرار الطباعة في جدول المبيعات
 */
function updateSalesTablePrintButtons() {
    // البحث عن جدول المبيعات وتحديث الأزرار
    const salesTable = document.getElementById('salesHistoryTable');
    if (!salesTable) return;

    // إضافة أزرار الطباعة الجديدة لكل صف
    const rows = salesTable.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const actionsCell = row.querySelector('td:last-child');
        if (actionsCell) {
            const saleId = actionsCell.querySelector('button').getAttribute('onclick').match(/'([^']+)'/)[1];

            // إضافة أزرار الطباعة الجديدة
            actionsCell.innerHTML = `
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-info dropdown-toggle" data-toggle="dropdown">
                        <i class="fas fa-print"></i>
                    </button>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" href="#" onclick="printThermalInvoice('${saleId}')">
                            <i class="fas fa-receipt"></i> فاتورة حرارية
                        </a>
                        <a class="dropdown-item" href="#" onclick="printStandardInvoice('${saleId}')">
                            <i class="fas fa-file-invoice"></i> فاتورة عادية
                        </a>
                        <a class="dropdown-item" href="#" onclick="printSaleItemsLabels('${saleId}')">
                            <i class="fas fa-barcode"></i> ملصقات الباركود
                        </a>
                    </div>
                </div>
            `;
        }
    });
}

/**
 * طباعة الفاتورة
 */
function printInvoice(saleData) {
    // إذا تم تمرير معرف البيع، احصل على بيانات البيع
    const sale = typeof saleData === 'string' ? db.getSale(saleData) : saleData;

    if (!sale) {
        app.showAlert('خطأ', 'لم يتم العثور على بيانات البيع');
        return;
    }

    const customer = sale.customerId !== 'guest' ? db.getCustomer(sale.customerId) : null;
    const settings = db.getSettings();
    
    // إنشاء رقم فاتورة فريد
    const invoiceNumber = sale.invoiceNumber || `INV-${new Date().getFullYear()}-${sale.id.substring(0, 8)}`;
    const currentDate = new Date();
    const hijriDate = db.toHijriDate ? db.toHijriDate(currentDate) : '';

    const invoiceContent = `
        <div class="invoice">
            <div class="invoice-header">
                <div class="store-info">
                    <h1>${(settings.company && settings.company.companyName) || settings.storeName || 'تكنوفلاش'}</h1>
                    <p class="store-address">${(settings.company && settings.company.address) || settings.storeAddress || 'العنوان غير محدد'}</p>
                    <p class="store-contact">
                        <span>📞 ${(settings.company && settings.company.phone) || settings.storePhone || 'غير محدد'}</span>
                        ${((settings.company && settings.company.email) || settings.storeEmail) ? `<span>📧 ${(settings.company && settings.company.email) || settings.storeEmail}</span>` : ''}
                    </p>
                    ${((settings.company && settings.company.taxNumber) || settings.taxNumber) ? `<p class="tax-number">الرقم الضريبي: ${(settings.company && settings.company.taxNumber) || settings.taxNumber}</p>` : ''}
                    ${((settings.company && settings.company.commercialRegister)) ? `<p class="commercial-register">السجل التجاري: ${settings.company.commercialRegister}</p>` : ''}
                </div>

                <div class="invoice-info">
                    <h2>فاتورة مبيعات</h2>
                    <div class="invoice-details">
                        <p><strong>رقم الفاتورة:</strong> ${invoiceNumber}</p>
                        <p><strong>التاريخ الميلادي:</strong> ${db.formatDate(sale.date, true)}</p>
                        ${hijriDate ? `<p><strong>التاريخ الهجري:</strong> ${hijriDate}</p>` : ''}
                        <p><strong>الوقت:</strong> ${new Date(sale.date).toLocaleTimeString('ar-SA')}</p>
                    </div>
                </div>
            </div>

            <div class="customer-info">
                <h3>بيانات العميل</h3>
                <p><strong>الاسم:</strong> ${customer ? customer.name : 'عميل عادي'}</p>
                ${customer && customer.phone ? `<p><strong>الهاتف:</strong> ${customer.phone}</p>` : ''}
                ${customer && customer.address ? `<p><strong>العنوان:</strong> ${customer.address}</p>` : ''}
            </div>
            
            <div class="items-section">
                <h3>تفاصيل المنتجات</h3>
                <table class="invoice-table">
                    <thead>
                        <tr>
                            <th class="item-no">#</th>
                            <th class="item-name">اسم المنتج</th>
                            <th class="item-code">الكود</th>
                            <th class="item-price">السعر</th>
                            <th class="item-qty">الكمية</th>
                            <th class="item-total">الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${sale.items.map((item, index) => `
                            <tr>
                                <td class="item-no">${db.toArabicNumerals(index + 1)}</td>
                                <td class="item-name">${item.name}</td>
                                <td class="item-code">${item.barcode || '-'}</td>
                                <td class="item-price">${db.formatCurrency(item.price)}</td>
                                <td class="item-qty">${db.toArabicNumerals(item.quantity)}</td>
                                <td class="item-total">${db.formatCurrency(item.total)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                    <tfoot>
                        <tr class="total-row">
                            <td colspan="5"><strong>المجموع الفرعي</strong></td>
                            <td><strong>${db.formatCurrency(sale.subtotal)}</strong></td>
                        </tr>
                        ${sale.discountAmount > 0 ? `
                        <tr class="discount-row">
                            <td colspan="5">الخصم (${sale.discount}%)</td>
                            <td>-${db.formatCurrency(sale.discountAmount)}</td>
                        </tr>` : ''}
                        ${sale.taxAmount > 0 ? `
                        <tr class="tax-row">
                            <td colspan="5">الضريبة (${sale.tax}%)</td>
                            <td>${db.formatCurrency(sale.taxAmount)}</td>
                        </tr>` : ''}
                        <tr class="grand-total-row">
                            <td colspan="5"><strong>الإجمالي النهائي</strong></td>
                            <td><strong>${db.formatCurrency(sale.total)}</strong></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            
            <div class="payment-info">
                <div class="payment-method">
                    <h3>معلومات الدفع</h3>
                    <p><strong>طريقة الدفع:</strong> ${getPaymentMethodText(sale.paymentMethod)}</p>
                    <p><strong>المبلغ المدفوع:</strong> ${db.formatCurrency(sale.total)}</p>
                    <p><strong>المبلغ بالكلمات:</strong> ${numberToWords(sale.total)} ريال سعودي</p>
                </div>
            </div>

            ${sale.notes ? `
            <div class="notes-section">
                <h3>ملاحظات</h3>
                <p>${sale.notes}</p>
            </div>` : ''}

            <div class="invoice-footer">
                <div class="footer-content">
                    <p class="thank-you">شكراً لتسوقكم معنا</p>
                    <p class="return-policy">سياسة الإرجاع: يمكن إرجاع المنتجات خلال 7 أيام من تاريخ الشراء</p>
                    <div class="qr-section">
                        <p>للاستعلام عن الفاتورة امسح الكود:</p>
                        <div class="qr-placeholder">[QR Code]</div>
                    </div>
                </div>
                <div class="print-info">
                    <p>تم الطباعة في: ${new Date().toLocaleString('ar-SA')}</p>
                    <p>نظام تكنوفلاش لإدارة نقاط البيع</p>
                </div>
            </div>
        </div>
    `;
    
    // إنشاء نافذة طباعة
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة مبيعات</title>
            <style>
                * { box-sizing: border-box; }
                body {
                    font-family: 'Cairo', 'Segoe UI', Tahoma, Arial, sans-serif;
                    direction: rtl;
                    margin: 0;
                    padding: 20px;
                    background: #fff;
                    color: #333;
                    line-height: 1.6;
                }
                .invoice {
                    max-width: 800px;
                    margin: 0 auto;
                    background: #fff;
                    border: 2px solid #ddd;
                    border-radius: 10px;
                    overflow: hidden;
                }
                .invoice-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    text-align: center;
                    position: relative;
                }
                .invoice-header::after {
                    content: '';
                    position: absolute;
                    bottom: -10px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 0;
                    height: 0;
                    border-left: 20px solid transparent;
                    border-right: 20px solid transparent;
                    border-top: 10px solid #764ba2;
                }
                .store-info h1 {
                    margin: 0 0 10px 0;
                    font-size: 2.5em;
                    font-weight: bold;
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                }
                .store-address {
                    font-size: 1.1em;
                    margin: 5px 0;
                    opacity: 0.9;
                }
                .store-contact {
                    margin: 10px 0;
                    font-size: 1em;
                }
                .store-contact span {
                    margin: 0 15px;
                    display: inline-block;
                }
                .tax-number {
                    font-size: 0.9em;
                    margin-top: 10px;
                    opacity: 0.8;
                }
                .invoice-info {
                    background: rgba(255,255,255,0.1);
                    padding: 20px;
                    margin-top: 20px;
                    border-radius: 10px;
                    backdrop-filter: blur(10px);
                }
                .invoice-info h2 {
                    margin: 0 0 15px 0;
                    font-size: 1.8em;
                    text-align: center;
                }
                .invoice-details p {
                    margin: 8px 0;
                    font-size: 1em;
                }
                .customer-info, .items-section, .payment-info, .notes-section {
                    padding: 25px;
                    border-bottom: 1px solid #eee;
                }
                .customer-info h3, .items-section h3, .payment-info h3, .notes-section h3 {
                    color: #667eea;
                    border-bottom: 2px solid #667eea;
                    padding-bottom: 10px;
                    margin-bottom: 15px;
                    font-size: 1.3em;
                }
                .invoice-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 15px 0;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    border-radius: 8px;
                    overflow: hidden;
                }
                .invoice-table th {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 15px 10px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 0.9em;
                }
                .invoice-table td {
                    padding: 12px 10px;
                    text-align: center;
                    border-bottom: 1px solid #eee;
                    font-size: 0.9em;
                }
                .invoice-table tbody tr:hover {
                    background: #f8f9ff;
                }
                .item-no { width: 8%; }
                .item-name { width: 35%; text-align: right !important; }
                .item-code { width: 15%; }
                .item-price { width: 14%; }
                .item-qty { width: 10%; }
                .item-total { width: 18%; }
                .invoice-table tfoot td {
                    padding: 10px;
                    font-weight: bold;
                    background: #f8f9fa;
                }
                .total-row td { background: #e3f2fd !important; }
                .discount-row td { background: #fff3e0 !important; color: #f57c00; }
                .tax-row td { background: #f3e5f5 !important; color: #7b1fa2; }
                .grand-total-row td {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                    color: white !important;
                    font-size: 1.1em;
                }
                .payment-method p {
                    margin: 10px 0;
                    font-size: 1.1em;
                }
                .invoice-footer {
                    background: #f8f9fa;
                    padding: 30px;
                    text-align: center;
                    border-top: 3px solid #667eea;
                }
                .thank-you {
                    font-size: 1.5em;
                    color: #667eea;
                    font-weight: bold;
                    margin-bottom: 15px;
                }
                .return-policy {
                    font-size: 0.9em;
                    color: #666;
                    margin: 10px 0;
                    font-style: italic;
                }
                .qr-section {
                    margin: 20px 0;
                }
                .qr-placeholder {
                    width: 80px;
                    height: 80px;
                    border: 2px dashed #ccc;
                    margin: 10px auto;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 0.8em;
                    color: #999;
                }
                .print-info {
                    margin-top: 20px;
                    padding-top: 20px;
                    border-top: 1px solid #ddd;
                    font-size: 0.8em;
                    color: #888;
                }
                @media print {
                    body { margin: 0; padding: 0; }
                    .invoice { border: none; border-radius: 0; }
                    .invoice-header::after { display: none; }
                    @page { margin: 1cm; }
                }
            </style>
        </head>
        <body>
            ${invoiceContent}
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

/**
 * الحصول على نص طريقة الدفع
 */
function getPaymentMethodText(method) {
    const methods = {
        cash: 'نقداً',
        credit: 'آجل',
        card: 'بطاقة'
    };
    return methods[method] || method;
}

/**
 * تحويل الرقم إلى كلمات باللغة العربية
 */
function numberToWords(num) {
    if (num === 0) return 'صفر';

    const ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
    const tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
    const teens = ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'];
    const hundreds = ['', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'];

    function convertHundreds(n) {
        let result = '';

        if (n >= 100) {
            result += hundreds[Math.floor(n / 100)] + ' ';
            n %= 100;
        }

        if (n >= 20) {
            result += tens[Math.floor(n / 10)] + ' ';
            n %= 10;
        } else if (n >= 10) {
            result += teens[n - 10] + ' ';
            return result.trim();
        }

        if (n > 0) {
            result += ones[n] + ' ';
        }

        return result.trim();
    }

    const integerPart = Math.floor(num);
    const decimalPart = Math.round((num - integerPart) * 100);

    let result = '';

    if (integerPart >= 1000) {
        const thousands = Math.floor(integerPart / 1000);
        result += convertHundreds(thousands) + ' ألف ';
        const remainder = integerPart % 1000;
        if (remainder > 0) {
            result += convertHundreds(remainder);
        }
    } else {
        result = convertHundreds(integerPart);
    }

    if (decimalPart > 0) {
        result += ' و ' + convertHundreds(decimalPart) + ' هللة';
    }

    return result.trim();
}

/**
 * عرض سجل المبيعات
 */
function showSalesHistory() {
    const sales = db.getSales();
    const customers = db.getCustomers();
    
    const tbody = document.querySelector('#salesHistoryTable tbody');
    
    if (sales.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center">لا توجد مبيعات حتى الآن</td>
            </tr>
        `;
    } else {
        tbody.innerHTML = sales
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .map(sale => {
                const customer = customers.find(c => c.id === sale.customerId);
                return `
                    <tr>
                        <td>${sale.invoiceNumber || sale.id.substr(-6)}</td>
                        <td>${customer ? customer.name : 'عميل عادي'}</td>
                        <td>${db.formatCurrency(sale.total)}</td>
                        <td>${getPaymentMethodText(sale.paymentMethod)}</td>
                        <td>${db.formatDate(sale.date, true)}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-info dropdown-toggle" data-toggle="dropdown" title="خيارات الطباعة">
                                    <i class="fas fa-print"></i>
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="#" onclick="printThermalInvoice('${sale.id}')">
                                        <i class="fas fa-receipt"></i> فاتورة حرارية
                                    </a>
                                    <a class="dropdown-item" href="#" onclick="printStandardInvoice('${sale.id}')">
                                        <i class="fas fa-file-invoice"></i> فاتورة عادية
                                    </a>
                                    <a class="dropdown-item" href="#" onclick="printSaleItemsLabels('${sale.id}')">
                                        <i class="fas fa-barcode"></i> ملصقات الباركود
                                    </a>
                                </div>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
    }
    
    app.showModal('salesHistoryModal');
}

/**
 * تحديث العملة في واجهة المبيعات
 */
function updateSalesCurrency(currency) {
    try {
        // تحديث العملة في العنصر الجذر
        document.documentElement.setAttribute('data-currency', currency);

        // تحديث جميع عناصر العملة في واجهة المبيعات
        const currencyElements = document.querySelectorAll('.currency, [data-currency], .amount, .price, .total');
        currencyElements.forEach(element => {
            element.setAttribute('data-currency', currency);
            element.classList.add('currency');
        });

        // إعادة حساب المجاميع
        if (typeof calculateTotal === 'function') {
            calculateTotal();
        }

        console.log('تم تحديث العملة في المبيعات:', currency);
    } catch (error) {
        console.error('خطأ في تحديث العملة في المبيعات:', error);
    }
}

/**
 * تحديث نوع الأرقام في واجهة المبيعات
 */
function updateSalesNumbers(numberType) {
    try {
        // تحديث نوع الأرقام في العنصر الجذر
        document.documentElement.setAttribute('data-number-type', numberType);

        // تحديث جميع عناصر الأرقام
        const numberElements = document.querySelectorAll('.number, .amount, .price, .total, .quantity');
        numberElements.forEach(element => {
            element.classList.add('number');
        });

        // إعادة حساب المجاميع
        if (typeof calculateTotal === 'function') {
            calculateTotal();
        }

        console.log('تم تحديث نوع الأرقام في المبيعات:', numberType);
    } catch (error) {
        console.error('خطأ في تحديث نوع الأرقام في المبيعات:', error);
    }
}

/**
 * تحديث إعدادات نقطة البيع
 */
function updatePOSSettings(posSettings) {
    try {
        console.log('تحديث إعدادات نقطة البيع في المبيعات:', posSettings);

        // تحديث طريقة الدفع الافتراضية
        if (posSettings.defaultPaymentMethod) {
            const paymentMethodSelect = document.querySelector('[name="paymentMethod"]');
            if (paymentMethodSelect) {
                paymentMethodSelect.value = posSettings.defaultPaymentMethod;
                currentSale.paymentMethod = posSettings.defaultPaymentMethod;
                console.log('تم تحديث طريقة الدفع الافتراضية:', posSettings.defaultPaymentMethod);
            }
        }

        // تحديث إعدادات الحفظ التلقائي
        if (typeof posSettings.autoSave !== 'undefined') {
            window.autoSaveEnabled = posSettings.autoSave;
            console.log('تم تحديث الحفظ التلقائي:', posSettings.autoSave);
        }

        // تحديث إعدادات الطباعة التلقائية
        if (typeof posSettings.autoPrint !== 'undefined') {
            window.autoPrintEnabled = posSettings.autoPrint;
            console.log('تم تحديث الطباعة التلقائية:', posSettings.autoPrint);
        }

        // تحديث إعدادات ماسح الباركود
        if (typeof posSettings.barcodeScanner !== 'undefined') {
            window.barcodeScannerEnabled = posSettings.barcodeScanner;
            console.log('تم تحديث ماسح الباركود:', posSettings.barcodeScanner);
        }

        // تحديث تنبيهات المخزون المنخفض
        if (typeof posSettings.lowStockAlert !== 'undefined') {
            window.lowStockAlertEnabled = posSettings.lowStockAlert;
            console.log('تم تحديث تنبيهات المخزون المنخفض:', posSettings.lowStockAlert);
        }

        // تحديث حد المخزون المنخفض
        if (posSettings.lowStockThreshold) {
            window.lowStockThreshold = posSettings.lowStockThreshold;
            console.log('تم تحديث حد المخزون المنخفض:', posSettings.lowStockThreshold);
        }

        // تحديث طول رقم الفاتورة
        if (posSettings.invoiceNumberLength) {
            window.invoiceNumberLength = posSettings.invoiceNumberLength;
            console.log('تم تحديث طول رقم الفاتورة:', posSettings.invoiceNumberLength);
        }

        // حفظ إعدادات نقطة البيع في متغير عام
        window.posSettings = posSettings;

        console.log('تم تحديث جميع إعدادات نقطة البيع بنجاح');
    } catch (error) {
        console.error('خطأ في تحديث إعدادات نقطة البيع:', error);
    }
}

/**
 * تحديث بيانات الشركة في واجهة المبيعات
 */
function updateCompanyInfo(companySettings) {
    try {
        console.log('تحديث بيانات الشركة في المبيعات:', companySettings);

        // تحديث اسم الشركة في العناصر
        const companyNameElements = document.querySelectorAll('.company-name');
        companyNameElements.forEach(element => {
            element.textContent = companySettings.companyName || 'تكنوفلاش';
        });

        // حفظ بيانات الشركة للاستخدام في الفواتير
        window.companySettings = companySettings;

        console.log('تم تحديث بيانات الشركة في المبيعات');
    } catch (error) {
        console.error('خطأ في تحديث بيانات الشركة:', error);
    }
}

/**
 * وظائف طباعة آمنة مع معالجة الأخطاء
 */

/**
 * طباعة فاتورة حرارية آمنة
 */
async function safePrintThermalInvoice(saleId) {
    try {
        console.log('🖨️ بدء طباعة فاتورة حرارية آمنة:', saleId);

        if (typeof printThermalInvoice === 'undefined') {
            throw new Error('وظيفة الطباعة الحرارية غير متوفرة. تأكد من تحميل ملف thermal-printer.js');
        }

        const sale = db.getSale(saleId);
        if (!sale) {
            throw new Error('لم يتم العثور على بيانات البيع');
        }

        await printThermalInvoice(sale);
        app.showNotification('تم إرسال الفاتورة الحرارية للطباعة', 'success');

    } catch (error) {
        console.error('❌ خطأ في الطباعة الحرارية:', error);
        app.showAlert('خطأ في الطباعة', error.message);
    }
}

/**
 * طباعة فاتورة عادية آمنة
 */
async function safePrintStandardInvoice(saleId) {
    try {
        console.log('🖨️ بدء طباعة فاتورة عادية آمنة:', saleId);

        if (typeof printStandardInvoice === 'undefined') {
            throw new Error('وظيفة الطباعة العادية غير متوفرة. تأكد من تحميل ملف thermal-printer.js');
        }

        const sale = db.getSale(saleId);
        if (!sale) {
            throw new Error('لم يتم العثور على بيانات البيع');
        }

        await printStandardInvoice(sale);
        app.showNotification('تم إرسال الفاتورة العادية للطباعة', 'success');

    } catch (error) {
        console.error('❌ خطأ في الطباعة العادية:', error);
        app.showAlert('خطأ في الطباعة', error.message);
    }
}

/**
 * طباعة ملصقات الباركود آمنة
 */
async function safePrintSaleItemsLabels(saleId) {
    try {
        console.log('🏷️ بدء طباعة ملصقات الباركود آمنة:', saleId);

        if (typeof printSaleItemsLabels === 'undefined') {
            throw new Error('وظيفة طباعة الملصقات غير متوفرة. تأكد من تحميل ملف thermal-printer.js');
        }

        const sale = db.getSale(saleId);
        if (!sale) {
            throw new Error('لم يتم العثور على بيانات البيع');
        }

        await printSaleItemsLabels(sale);
        app.showNotification('تم إرسال ملصقات الباركود للطباعة', 'success');

    } catch (error) {
        console.error('❌ خطأ في طباعة الملصقات:', error);
        app.showAlert('خطأ في الطباعة', error.message);
    }
}

/**
 * اختبار الطابعة الحرارية آمن
 */
async function safeTestThermalPrinter() {
    try {
        console.log('🧪 بدء اختبار الطابعة الحرارية الآمن');

        if (typeof testThermalPrinter === 'undefined') {
            throw new Error('وظيفة اختبار الطابعة غير متوفرة. تأكد من تحميل ملف thermal-printer.js');
        }

        await testThermalPrinter();
        app.showNotification('تم إرسال اختبار الطابعة', 'info');

    } catch (error) {
        console.error('❌ خطأ في اختبار الطابعة:', error);
        app.showAlert('خطأ في الاختبار', error.message);
    }
}
